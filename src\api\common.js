import request from '@/axios';

/**
 * 文件流返回
 * @param url 接口地址
 * @param params 接口参数
 */
export const exportBlob = (url, params) => {
  return request({
    url: url,
    params: params,
    method: 'get',
    responseType: 'blob',
  });
};

/**
 * 获取院系列表
 * @param {*} params 查询参数
 * @returns promise
 */
export const getYxListAPI = (params) => {
  return request({
    url: '/blade-yxb/Yxb/list',
    params,
    method: 'get',
  });
};


/**
 * 获取专业列表
 * @param {*} params 查询参数
 * @returns promise
 */
export const getZyListAPI = (params) => {
  return request({
    url: '/blade-zyszsj/zyszsj/list',
    params,
    method: 'get',
  });
}


/**
 * 获取年级列表
 * @param {*} params 查询参数
 * @returns promise
 */
export const getNjListAPI = (params) => {
  return request({
    url: '/blade-bjsj/Bjsj/njList',
    params,
    method: 'get',
  });
};

/**
 * 获取班级列表
 * @param {*} params 查询参数
 * @returns promise
 */
export const getBjListAPI = (params) => {
  return request({
    url: '/blade-bjsj/Bjsj/list',
    params,
    method: 'get',
  });
};