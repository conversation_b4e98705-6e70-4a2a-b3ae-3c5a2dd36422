<template>
  <cm-container @onLoad="onLoad">
    <div
      class="ssw-message"
      element-loading-text="加载中..."
      v-loading="loading"
      :element-loading-svg="svg"
      element-loading-background="rgba(255, 255, 255, 0.5)"
      element-loading-svg-view-box="-10, -10, 50, 50">
      <div class="ssw-message-search">
        <div class="ssw-message-search-item">
          <el-date-picker style="width: 200px" v-model="searchParams.beginTime" type="date" placeholder="请选择日期" />
          <el-select v-model="searchParams.lx" placeholder="请选择消息类型" style="width: 200px">
            <el-option v-for="item in yjTsXxJlXxlxDictData" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <div class="ssw-message-search-btn">
          <el-button type="primary" @click="onSearch"> 搜索 </el-button>
          <el-button @click="onClear">清空</el-button>
        </div>
      </div>
      <div class="message-operations" v-if="hasAuth('message:readAll')">
        <el-button type="primary" size="small" @click="onReadAll">全部标记已读</el-button>
      </div>
      <div class="message-table" ref="tableRef">
        <el-table :data="tableData" style="width: 100%" :height="tableHeight" show-overflow-tooltip>
          <el-table-column type="index" label="序号" width="55" align="center" />
          <el-table-column prop="lx" label="消息类型" width="150" align="center" />
          <el-table-column prop="xxnr" label="消息内容" align="center" />
          <el-table-column prop="createTime" label="发送时间" width="180" align="center" />
          <el-table-column prop="jsrlx" label="接收人" width="100" align="center" />
          <el-table-column prop="sfyd" label="是否已读" width="100" align="center" :formatter="(item) => (item.sfyd === 1 ? '是' : '否')" />
          <el-table-column prop="prop" label="操作" width="80" align="center" v-if="hasAuth('message:view')">
            <template #default="scope">
              <el-button type="primary" link @click="onView(scope.row)">查看</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="message-pagination">
        <el-pagination
          v-model:current-page="pageParams.current"
          v-model:page-size="pageParams.size"
          background
          layout="total, sizes, prev, pager, next, jumper"
          :total="pageParams.total"
          @change="onLoad" />
      </div>
    </div>
    <cm-dialog v-model="dialogVisible" title="消息详情" width="600px" class="message-view-dialog">
      <div class="message-detail">
        <el-descriptions :column="2" border label-width="100">
          <el-descriptions-item label="消息类型">{{ detailData.lx }}</el-descriptions-item>
          <el-descriptions-item label="发送时间">{{ detailData.createTime }}</el-descriptions-item>
          <el-descriptions-item label="接收人">{{ detailData.jsrlx }}</el-descriptions-item>
          <el-descriptions-item label="是否已读">
            <el-tag size="small">是</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="消息内容">{{ detailData.xxnr }}</el-descriptions-item>
        </el-descriptions>
      </div>
    </cm-dialog>
  </cm-container>
</template>

<script setup>
import { onMounted } from "vue";
import CmContainer from "@/components/cm-container/main.vue";
import { useResizeObserver } from "@/hooks/useResizeObserver";
import { hasAuth } from "@/utils/auth";
import CmDialog from "@/components/cm-dialog/index.vue";
import { ElMessageBox, ElMessage } from "element-plus";
import { getMessageListAPI, allReadAPI, viewMessageAPI } from "@/api/message";
import useDict from "@/hooks/useDict";
const { yjTsXxJlXxlxDictData } = useDict("yj_ts_xx_jl_xxlx");
const loading = ref(true);
const searchParams = reactive({
  beginTime: "",
  lx: "",
});
const pageParams = reactive({
  current: 1,
  size: 10,
  total: 0,
});
const dialogVisible = ref(false);
const svg = `
        <path class="path" d="
          M 30 15
          L 28 17
          M 25.61 25.61
          A 15 15, 0, 0, 1, 15 30
          A 15 15, 0, 1, 1, 27.99 7.5
          L 15 15
        " style="stroke-width: 4px; fill: rgba(0, 0, 0, 0)"/>
      `;
const tableRef = useTemplateRef("tableRef");
const tableHeight = ref(0);
onMounted(() => {
  useResizeObserver(tableRef, (entries) => {
    const entry = entries[0];
    const { height: _h } = entry.contentRect;
    tableHeight.value = _h - 2;
  });
});
const tableData = ref([]);
const onLoad = () => {
  loading.value = false;
  getMessageListAPI({
    current: pageParams.current,
    size: pageParams.size,
    ...searchParams,
  }).then((reslut) => {
    const {
      data: {
        data: { records = [], total = 0 },
      },
    } = reslut;
    tableData.value = records;
    pageParams.total = total;
  });
};
const onSearch = () => {
  pageParams.current = 1;
  onLoad();
};
const onClear = () => {
  pageParams.current = 1;
  searchParams.lx = void 0;
  searchParams.beginTime = void 0;
  onLoad();
};

const detailData = ref({});
const onView = (row) => {
  viewMessageAPI({
    id: row.id,
  }).then((reslut) => {
    const {
      data: { data = {}, code },
    } = reslut;
    if (code === 200) {
      row.sfyd = 1;
      detailData.value = data;
      dialogVisible.value = true;
    }
  });
};
const onReadAll = () => {
  ElMessageBox.confirm("确定将所有消息标记为已读吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      allReadAPI(searchParams).then(() => {
        ElMessage.success("标记已读成功");
        onLoad();
      });
    })
    .catch(() => {});
};
</script>

<style lang="scss" scoped>
.ssw-message {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #fff;
  box-sizing: border-box;
  padding: 12px;
  gap: 12px;

  .ssw-message-search {
    display: flex;
    align-items: center;
    column-gap: 12px;

    .ssw-message-search-item {
      @extend .ssw-message-search;
    }
  }

  .message-table {
    flex: 1;
    overflow: hidden;
  }

  .message-pagination {
    display: flex;
    justify-content: flex-end;
  }
}

.message-view-dialog {
  .message-detail {
    padding: 20px;
  }
}
</style>
