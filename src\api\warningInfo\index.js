import request from '@/axios';


//班级列表
export const bjsjListApi = (params) => {
    return request({
      url: '/blade-bjsj/Bjsj/list',
      method: 'get',
      params
    });
};
// 明细
export const yjxxCjListApi = (params) => {
  return request({
    url: '/blade-yjxxcj/yjxxCj/page',
    method: 'get',
    params
  });
};

//处理
export const handleApi = (data) => {
  return request({
    url: '/blade-yjxxcj/yjxxCj/handle',
    method: 'post',
    data
  });
};

// 详情
export const xxDetailApi = (params) => {
  return request({
    url: '/blade-yjxxcj/yjxxCj/xxDetail',
    method: 'get',
    params
  });
};

