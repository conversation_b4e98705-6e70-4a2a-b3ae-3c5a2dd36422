<template>
  <cm-container @onLoad="onLoad">
    <div
      class="alarm-settings"
      element-loading-text="加载中..."
      v-loading="loading"
      :element-loading-svg="svg"
      element-loading-background="rgba(255, 255, 255, 0.5)"
      element-loading-svg-view-box="-10, -10, 50, 50">
      <div class="alarm-settings-search">
        <el-select style="width: 240px" v-model="searchParams.yjlx" placeholder="全部预警批次" clearable>
          <el-option v-for="item in yjlxList" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <el-select style="width: 240px" v-model="searchParams.yjlx" placeholder="预警类型" clearable>
          <el-option v-for="item in yjlxList" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <el-select style="width: 240px" v-model="searchParams.tszt" placeholder="核验状态" clearable>
          <el-option v-for="item in tsztList" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <el-select style="width: 240px" v-model="searchParams.tszt" placeholder="推送状态" clearable>
          <el-option v-for="item in tsztList" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <div class="alarm-settings-search-button">
          <el-button type="primary" @click="onSearch"> 搜索 </el-button>
          <el-button @click="onClear">清空</el-button>
        </div>
      </div>
      <div class="alarm-settings-operations">
        <el-button type="primary" size="small" @click="dialogBysjVisible = true">新建自动预警</el-button>
        <el-button type="primary" size="small" @click="onJumpAutogeneration('bdsjAll')">设置自动预警</el-button>
        <el-switch
          v-model="switchValue"
          active-color="#333"
          inactive-color="#333"
          active-value="1"
          inactive-value="0"
          style="position: absolute; right: 0"
          :inactive-text="switchValue == 1 ? '关闭自动预警' : '开启自动预警'"
          @change="onSwitchChange">
        </el-switch>
      </div>
      <div class="alarm-settings-table" ref="tableRef">
        <el-table :data="tableData" style="width: 100%" :height="tableHeight" @selection-change="handleSelectionChange">
          <el-table-column type="index" width="55" align="center" />
          <el-table-column prop="yjbh" label="预警批次" />
          <el-table-column prop="yjlx" label="预警类型">
            <!-- 1：手动预警，2：自动预警 -->
            <template #default="scope">
              <span v-if="scope.row.yjlx == '1'">手动预警</span>
              <span v-if="scope.row.yjlx == '2'">自动预警</span>
            </template>
          </el-table-column>
          <el-table-column prop="tsdx" label="预警学生范围">
            <!-- 1-非毕业生 2-毕业生 -->
            <template #default="scope">
              <span v-if="scope.row.tsdx == '1'">非毕业生</span>
              <span v-if="scope.row.tsdx == '2'">毕业生</span>
            </template>
          </el-table-column>
          <el-table-column prop="xxsczt" label="预警信息生成状态">
            <!-- （0：未生成，1：生成中，2：已生成） -->
            <template #default="scope">
              <span v-if="scope.row.xxsczt == '0'" class="table-danger">未生成</span>
              <span v-if="scope.row.xxsczt == '1'" class="table-warning">生成中</span>
              <span v-if="scope.row.xxsczt == '2'" class="table-success">已生成</span>
            </template>
          </el-table-column>
          <el-table-column prop="hyzt" label="核验状态">
            <!-- 0：待核验，1：已人工核验，2：已自动核验 -->
            <template #default="scope">
              <span v-if="scope.row.hyzt == '0'" class="table-danger">待人工核验</span>
              <span v-if="scope.row.hyzt == '1'" class="table-warning">已人工核验</span>
              <span v-if="scope.row.hyzt == '2'" class="table-success">已自动核验</span>
            </template>
          </el-table-column>
          <el-table-column prop="tszt" label="推送状态">
            <!-- 0：未推送，1：已手动推送，2：已自动推送 -->
            <template #default="scope">
              <span v-if="scope.row.tszt == '0'" class="table-danger">未推送</span>
              <span v-if="scope.row.tszt == '1'" class="table-warning">已手动推送</span>
              <span v-if="scope.row.tszt == '2'" class="table-success">已自动推送</span>
            </template>
          </el-table-column>
          <el-table-column prop="yjtssj" label="预警推送时间" />
          <el-table-column prop="tsfs" label="推送方式" width="100" />
          <el-table-column label="操作" width="150" align="center">
            <template #default="scope">
              <el-button type="primary" link @click="onJumpInfo(scope.row)">查看明细</el-button>
              <el-button type="primary" link @click="onClearSettings(scope.row)">刷新生成状态</el-button>
              <el-button type="primary" link @click="onClearSettings(scope.row)">生成预警</el-button>
              <el-button type="primary" link @click="onClearSettings(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="cm-pagination-right">
          <el-pagination
            v-model:current-page="pageParams.current"
            v-model:page-size="pageParams.size"
            background
            layout="total, sizes, prev, pager, next, jumper"
            :total="pageParams.total"
            @change="onLoad" />
        </div>
      </div>
    </div>
    <cm-dialog v-model="dialogBysjVisible" title="新建手动预警" width="500px" :close-on-click-modal="false" :close-on-press-escape="false" @close="ruleForm.bysjfs = void 0" class="cm-dialog">
      <template #default>
        <div class="alarm-settings-dialog-content">
          <div class="warning-tip">【温馨提示】由于平台预警信息数据较多，生成预警信息需用时几分钟完成，请稍后于列表操作栏处刷新状态，即可查看预警信息明细内容。</div>
          <el-form ref="ruleFormBysjRef" :model="ruleForm" :rules="rules" label-width="auto">
            <el-form-item label="预警批次">
              <el-input v-model="ruleForm.yjbh" disabled class="inp-text-left" :controls="false" placeholder="请输入预警批次" />
            </el-form-item>
            <el-form-item label="预警时间">
              <el-input v-model="ruleForm.yjcjrq" disabled class="inp-text-left" :controls="false" placeholder="请输入预警时间" />
            </el-form-item>
            <el-form-item label="预警类型">
              <el-input v-model="ruleForm.yjlx" disabled class="inp-text-left" :controls="false" placeholder="请输入预警类型" />
            </el-form-item>
            <el-form-item label="统计学生范围">
              <el-input v-model="ruleForm.tjxsfw" disabled class="inp-text-left" :controls="false" placeholder="请输入统计学生范围" />
            </el-form-item>
            <el-form-item label="预警学生范围" prop="tsdx">
              <el-checkbox-group v-model="ruleForm.tsdx">
                <el-checkbox :label="1" :key="1">非毕业生</el-checkbox>
                <el-checkbox :label="2" :key="2">毕业生</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </el-form>
        </div>
      </template>
      <template #footer>
        <el-button @click="dialogBysjVisible = false">取消</el-button>
        <el-button type="primary" @click="onConfirmBysj" :loading="approvalBysjLoading"> 确 定 </el-button>
      </template>
    </cm-dialog>
  </cm-container>
</template>

<script setup>
import { useRouter, useRoute } from "vue-router";
import { ElMessage } from "element-plus";
import CmContainer from "@/components/cm-container/main.vue";
import { useResizeObserver } from "@/hooks/useResizeObserver";
import { tsXxList, updateByKey, detail, addTsXx } from "@/api/alarmingPush/alarmingPush";
import CmDialog from "@/components/cm-dialog/index.vue";
import { getListAPI } from "@/api/alarmSettings";
import dayjs from "dayjs";
const loading = ref(true);
const svg = `
          <path class="path" d="
            M 30 15
            L 28 17
            M 25.61 25.61
            A 15 15, 0, 0, 1, 15 30
            A 15 15, 0, 1, 1, 27.99 7.5
            L 15 15
          " style="stroke-width: 4px; fill: rgba(0, 0, 0, 0)"/>
        `;

const ruleForm = reactive({
  yjbh: dayjs().format("YYYYMMDD"),
  yjcjrq: dayjs().format("YYYY-MM-DD"),
  yjlx: "手动预警",
  tjxsfw: "在籍在校学生",
  tsdx: [],
});
const rules = reactive({
  tsdx: [{ required: true, message: "请选择预警学生范围", trigger: "blur" }],
});

/**
 * 表格引用
 * @type {Ref<HTMLElement>}
 */
const tableRef = useTemplateRef("tableRef");

/**
 * 表格高度
 * @type {Ref<number>}
 */
const tableHeight = ref(0);

/**
 * 分页参数
 * @type {Ref<Object>}
 * @property {number} total 总条数
 * @property {number} current 当前页
 * @property {number} size 每页条数
 */
const pageParams = ref({
  total: 0,
  current: 1,
  size: 10,
});

/**
 * 搜索参数
 * @type {Ref<Object>}
 * @property {string} userName 申请人
 * @property {string} userCode 工号
 * @property {string} deptName 所属部门
 */
const searchParams = ref({
  nj: "",
  yxbh: "",
  zyh: "",
});
const yjlxList = ref([
  {
    value: "1",
    label: "手动预警",
  },
  {
    value: "2",
    label: "自动预警",
  },
]);
const tsztList = ref([
  {
    value: "0",
    label: "未推送",
  },
  {
    value: "1",
    label: "已手动推送",
  },
  {
    value: "2",
    label: "已自动推送",
  },
]);
/**
 * 初始化
 */
onMounted(() => {
  useResizeObserver(tableRef, (entries) => {
    const entry = entries[0];
    const { height: _h } = entry.contentRect;
    tableHeight.value = _h - 54;
  });
});
const router = useRouter();
const onJumpInfo = (row) => {
  sessionStorage.setItem(
    "alarmingPushSearchData",
    JSON.stringify({
      data: {
        yjbh: row.yjbh,
      },
      active: 1,
      activeInIndex: 0,
    }),
  );
  router.push({
    path: "/alarmingPush/info",
  });
};

const onClearSettings = (row) => {
  console.log(row);
};

/**
 * 搜索
 */
const onSearch = () => {
  pageParams.value.current = 1;
  onLoad();
};

/**
 * 清空搜索
 */
const onClear = () => {
  searchParams.value = {
    nj: "",
    yxbh: "",
    zyh: "",
  };
  pageParams.value.current = 1;
  onLoad();
};

/**
 * 加载数据
 */
const tableData = ref([]);
const onLoad = () => {
  loading.value = true;
  tsXxList(pageParams.value.current, pageParams.value.size, searchParams.value)
    .then((result) => {
      const { data, code } = result.data || {};
      if (code === 200) {
        tableData.value = data.records;
        pageParams.value.total = data.total;
      }
    })
    .finally(() => {
      loading.value = false;
    });
};
const switchValue = ref(false);
const onSwitchChange = () => {
  updateByKey({
    paramKey: "alarm.auto",
    paramValue: switchValue.value == 1 ? "0" : "1",
  }).then((result) => {
    // ElMessage.success('操作成功');
  });
};
detail({
  paramKey: "alarm.auto",
}).then((result) => {
  const { data, code } = result.data || {};
  if (code === 200) {
    switchValue.value = data.paramValue;
  }
});

const selectedRows = ref([]);
const handleSelectionChange = (rows) => {
  selectedRows.value = rows.map((item) => {
    return {
      id: item.id,
      nj: item.nj,
      zybm: item.zyh,
    };
  });
};

//毕业设计设置
const ruleFormBysjRef = useTemplateRef("ruleFormBysjRef");
const dialogBysjVisible = ref(false);
const approvalBysjLoading = ref(false);
const onJumpAutogeneration = () => {
  router.push({
    path: "/alarmingPush/autogeneration",
  });
};
const onConfirmBysj = () => {
  ruleFormBysjRef.value.validate((valid) => {
    if (valid) {
      // console.log(ruleForm);
      let data = {
        yjbh: ruleForm.yjbh,
        yjcjrq: ruleForm.yjcjrq,
        tsdx: ruleForm.tsdx.length == 2 ? 0 : ruleForm.tsdx[0],
      };
      addTsXx(data).then((result) => {
        const { code } = result.data || {};
        if (code === 200) {
          dialogBysjVisible.value = false;
          onLoad();
          ElMessage.success("新增成功");
        }
      });
    }
  });
};
</script>

<style lang="scss" scoped>
.alarm-settings {
  width: 100%;
  height: 100%;
  background-color: #fff;
  padding: 0 10px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  gap: 12px;

  .alarm-settings-search {
    margin-top: 12px;
    display: flex;
    align-items: center;
    gap: 10px;

    .alarm-settings-search-button {
      display: flex;
      align-items: center;
    }
  }

  .alarm-settings-operations {
    display: flex;
    align-items: center;
    position: relative;
  }

  .alarm-settings-table {
    flex: 1;
  }
}

.cm-dialog {
  .alarm-settings-dialog-content {
    padding: 20px;
  }

  .alarm-date {
    background: #f9faff;
    border: 1px solid #ffffff;
    backdrop-filter: blur(10px);
    padding: 10px;
    box-sizing: border-box;

    .alarm-date-header {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .alarm-date-header-title {
        font-family: AppleSystemUIFont;
        font-size: 16px;
        color: #333333;
      }
    }

    .alarm-date-table {
      margin-top: 10px;
    }
  }
}
.warning-tip {
  background-color: #ffecec;
  color: #f56c6c;
  padding: 8px;
  margin-bottom: 10px;
}
.table-success {
  color: rgb(129, 179, 55);
}
.table-warning {
  color: rgb(252, 202, 0);
}
.table-danger {
  color: rgb(189, 49, 36);
}
</style>
