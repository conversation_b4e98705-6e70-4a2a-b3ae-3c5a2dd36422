<!-- 学院近5年预警趋势 -->
<template>
  <div class="alarm-trend" ref="schoolAlarmTrendDivRef">
    <div class="alarm-trend-title">
      <div class="alarm-trend-title-item"> 
        <div class="alarm-trend-title-line" style="background-color: #FFB900;"></div>
        <span>通信工程</span>
      </div>
      <div class="alarm-trend-title-item"> 
        <div class="alarm-trend-title-line" style="background-color: #28A2FF;"></div>
        <span>信息工程</span>
      </div>
      <div class="alarm-trend-title-item"> 
        <div class="alarm-trend-title-line" style="background-color: #00F0FF;"></div>
        <span>经济管理</span>
      </div>
      <div class="alarm-trend-title-item"> 
        <div class="alarm-trend-title-line" style="background-color: #02FE92;"></div>
        <span>军士教育</span>
      </div>
    </div>
    <div ref="schoolAlarmTrendRef" style="width: 100%; height: 100%"></div>
  </div>
</template>
<script setup>
    import * as echarts from 'echarts';
    import { useResizeObserver } from '@/hooks/useResizeObserver'
   const props = defineProps({
    data: {
        type: Object,
        default: () => ({}),
    },
    });
 const getArrByKey = (data, k) => {
    let key = k || 'value';
    let res = [];
    if (data) {
        data.forEach(function (t) {
            res.push(t[key]);
        });
    }
    return res;
    };
    
    // 获取时间轴数据
const getXAxisData = () => {
  // 所有学院的时间轴数据相同，使用第一个学院的数据
  const collegeNames = Object.keys(props.data);
  if (collegeNames.length === 0) return [];
  
  // 所有学院的时间轴数据相同，使用第一个学院的数据
  const firstCollegeData = props.data[collegeNames[0]];
  if (!firstCollegeData || !Array.isArray(firstCollegeData)) return [];
  
  return firstCollegeData.map(item => item.xqmc);
};
console.log(getXAxisData(),'getXAxisData');
// 格式化x轴标签文本
const formatXAxisLabel = (text) => {
  if (!text) return '';
  // 如果文本过长，进行换行处理
  if (text.length > 8) {
    return text.substring(0, 9) + '\n' + text.substring(9);
  }
  return text;
};


 const getCollegeData = (data, collegeName) => {
    return data[collegeName] || [];
  };
  const schoolAlarmTrendRef = ref(null);
    let classAlarmRefInstance = null;
    const schoolAlarmTrendDivRef = useTemplateRef('schoolAlarmTrendDivRef');
    const resizeChart = () => {
    classAlarmRefInstance.resize();
    }
   
    const createChartOption = (xxgcxy, jjglxy, txgcxy, jsjyxy) =>{
      return {
    tooltip: {
        trigger: 'axis',
    textStyle: {
      fontSize: 12, // 字体大小
    },
    axisPointer: {
      type: 'shadow',
    },
    },
    grid: {
    top: '20%',
    left: '3%',
    right: '3%',
    bottom: '5%',
    containLabel: true,
  },
    xAxis: {
        type: "category",
        data: getXAxisData(),
        axisPointer: {
        type: "shadow",
        },
         axisTick: {
      show: false,
    },
        axisLabel: {
      fontSize: 12,
      color: '#E7FCFF',
      margin: 10,
      // 处理标签过长问题
        formatter: function(value) {
          return formatXAxisLabel(value);
        },
        // 旋转标签以节省空间
        rotate: 0,
        // 设置标签间隔
        interval: 0,
        // 允许换行
        lineHeight: 14
      
    },
       axisLine: {
      show: true,
      lineStyle: {
        width: 1,
        color: 'rgba(239, 247, 253, .1)',
      },
    },
    },
    yAxis: {
        // name: "(次)        ",
        type: "value",
     nameGap: 30,
      nameTextStyle: {
        color: '#ffffff',
        fontWeight: 400,
        fontSize: 16,
      },
      axisLine: {
        show: true,
        lineStyle: {
          width: 1,
          color: 'rgba(239, 247, 253, .1)',
        },
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: 'rgba(239, 247, 253, .1)',
        },
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        fontSize: 16,
        color: '#E7FCFF',
      },
    },
    series: [
        {
        name: "通信工程",
        data: getArrByKey(txgcxy,'alarmNum'),
        type: "line",
        smooth: true,
        showSymbol: false,
        itemStyle: {
            normal: {
            lineStyle: {
                width: 2,
                color: '#FFB900',
            },
            },
        },
        areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
                offset: 0,
                color: "rgba(255,225,141,1)",
            },
            {
                offset: 1,
                color: "rgba(0,0,0, 0)",
            },
            ]),
        },
        },
        {
        name: "信息工程",
        data: getArrByKey(xxgcxy,'alarmNum'),
        type: "line",
        smooth: true,
        showSymbol: false,
        itemStyle: {
            normal: {
            lineStyle: {
                width: 2,
                color: '#28A2FF',
            },
            },
        },
        areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
                offset: 0,
                color: "rgba(12,137,214,1)",
            },
            {
                offset: 1,
                color: "rgba(0,0,0, 0)",
            },
            ]),
        },
        },
        {
        name: "经济管理",
        data: getArrByKey(jjglxy,'alarmNum'),
        type: "line",
        smooth: true,
        showSymbol: false,
        itemStyle: {
            normal: {
            lineStyle: {
                width: 2,
                color: '#00F0FF',
            },
            },
        },
        areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
                offset: 0,
                color: "rgba(2,206,254,1)",
            },
             {
                        offset: 1,
                        color: 'rgba(0,0,0, 0)'
                    }
            ]),
        },
        },
        {
        name: "军士教育",
        data: getArrByKey(jsjyxy,'alarmNum'),
        type: "line",
        smooth: true,
        showSymbol: false,
        itemStyle: {
            normal: {
            lineStyle: {
                width: 2,
                color: '#02FE92',
            },
            },
        },
        areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
                offset: 0,
                color: "rgba(2,254,146,1)",
            },
             {
                        offset: 1,
                        color: 'rgba(0,0,0, 0)'
                    }
            ]),
        },
        },
    ],
    }}


   // 初始化或更新图表
const initOrUpdateChart = () => {
  if (!classAlarmRefInstance) {
    classAlarmRefInstance = echarts.init(schoolAlarmTrendRef.value);
  }
  
  const xxgcxy = getCollegeData(props.data, '信息工程学院');
  const jjglxy = getCollegeData(props.data, '经济管理学院');
  const txgcxy = getCollegeData(props.data, '通信工程学院');
  const jsjyxy = getCollegeData(props.data, '军士教育学院');
  // 创建并设置图表配置
  const option = createChartOption(xxgcxy, jjglxy, txgcxy, jsjyxy);
  classAlarmRefInstance.setOption(option, true); // 使用 true 参数替换旧配置
};
   // 监听 props.data 的变化
watch(() => props.data, (newData) => {
  if (newData && Object.keys(newData).length > 0) {
    nextTick(() => {
      initOrUpdateChart();
    });
  }
}, { deep: true });
    
    onMounted(() => {
    useResizeObserver(schoolAlarmTrendDivRef, entries => {
        resizeChart();
    })
    nextTick(() => {
    // 初始化图表
        classAlarmRefInstance = echarts.init(schoolAlarmTrendRef.value);
      
        if (props.data && Object.keys(props.data).length > 0) {
    nextTick(() => {
      initOrUpdateChart();
    });
  }
    })
    });
</script>
<style lang="scss" scoped>
@use '@/styles/utils.scss' as utils;
.alarm-trend {
  position: relative;
  .alarm-trend-title {
    position: absolute;
    z-index: 99;
    top: utils.vw(20px);
    right: utils.vw(10px);
    display: flex;
    justify-content: flex-end;
    align-items: center;
    gap: utils.vw(10px);
    .alarm-trend-title-item {
      display: flex;
      align-items: center;
      gap: utils.vw(5px);
      font-size: utils.vw(12px);
      color: #fff;
      .alarm-trend-title-line {
        width: utils.vw(15px);
        height: utils.vh(3px);
      }
    }
  }
}
</style>