<!-- 预警专业热门度 -->
<template>
  <div class="class-alarm" ref="instituteAlarmMajorPopularityDivRef">
    
    <div ref="instituteAlarmMajorPopularityRef" style="width: 100%; height: 100%"></div>
  </div>
</template>
<script setup>
import * as echarts from 'echarts';
import 'echarts-wordcloud';
import { useResizeObserver } from '@/hooks/useResizeObserver'

const props = defineProps({
  data: {
    type: Object,
    default: () => ({}),
  },
});
const instituteAlarmMajorPopularityRef = ref(null);
let classAlarmRefInstance = null;
const instituteAlarmMajorPopularityDivRef = useTemplateRef('instituteAlarmMajorPopularityDivRef');
const resizeChart = () => {
  classAlarmRefInstance.resize();
}
const wordCloudData = ref(props.data)
const option = {
    tooltip: {
        show: true,
        position: 'top',
        textStyle: {
            fontSize: 14
        }
    },
    series: [{
        type: "wordCloud",
        // 网格大小，各项之间间距
        gridSize: 30,
        // 形状 circle 圆，cardioid  心， diamond 菱形，
        // triangle-forward 、triangle 三角，star五角星
        shape: 'circle',
        // 字体大小范围
        sizeRange: [15, 20],
        // 文字旋转角度范围
        rotationRange: [0, 0],
        // 旋转步值
        // rotationStep: 90,
        // 自定义图形
        // maskImage: maskImage,
        left: 'center',
        top: 'center',
        right: null,
        bottom: null,
        // 画布宽
        width: '96%',
        // 画布高
        height: '90%',
        // 是否渲染超出画布的文字
        drawOutOfBound: true,
        textStyle: {
                      color: function () {
  let colors = ['#6085E4', '#52A0F4', '#5957FF', '#3054B1', '#007BFF', '#73BAED', '#C29242', '#C6E3FB', '#A57575','#468153','#3D5DB9','#7FA1F8'];
  return colors[Math.floor(Math.random() * colors.length)];
},
      
           
            emphasis: {
                shadowBlur: 10,
                shadowColor: '#2ac'
            }
        },
        data: wordCloudData.value
    }]
};
onMounted(() => {
  console.log(props.data);
  
  useResizeObserver(instituteAlarmMajorPopularityDivRef, entries => {
    resizeChart();
  })
  nextTick(() => {
  // 初始化图表
    classAlarmRefInstance = echarts.init(instituteAlarmMajorPopularityRef.value);
    classAlarmRefInstance.setOption(option);
  })
});
</script>
<style lang="scss" scoped>
</style>