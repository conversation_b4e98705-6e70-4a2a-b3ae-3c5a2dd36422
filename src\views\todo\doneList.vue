<!-- 已办 -->
<template>
  <cm-container>
    <div class="warning-info">
      <!-- 温馨提示 -->
      <div class="warning-tip">
        【温馨提示】学生毕业的最终解释权归教务处所有。
      </div>
     <!-- 搜索框部分 -->
      <div class="search-box">
        <el-select v-model="searchParams.nj" style="width: 240px" placeholder="请选择年级">
          <el-option
            v-for="item in njList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
       
        <el-select v-model="searchParams.bjbh" style="width: 240px" filterable placeholder="请选择班级">
           <el-option
            v-for="item in bjList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
        <div class="search-box-btn">
          <el-button type="primary" @click="onSearch">搜索</el-button>
          <el-button @click="onClear">清空</el-button>
        </div>
      </div>
      <!-- 表格部分 -->
      <div class="warning-info-table" ref="tableRef">
        <el-table :data="tableData" style="width: 100%" :height="tableHeight">
          <el-table-column
          label="序号"
      type="index"
      width="60">
    </el-table-column>
          <el-table-column v-for="column in columns" :key="column.prop" :prop="column.prop" :label="column.label" :width="column.width">
            <template #default="scope">
              <!-- 自定义列内容 -->
              <span v-if="column.prop === 'operation'">
                <!-- 操作列的具体内容 -->
                <el-button size="mini" type="text" @click="handle(scope.$index, scope.row)">查看处理</el-button>
                <el-button size="mini" type="text" @click="view(scope.$index, scope.row)">查看明细</el-button>
              </span>
              <span v-else>{{ scope.row[column.prop] }}</span>
            </template>
          </el-table-column>
        </el-table>
        <div class="cm-pagination-right">
          <el-pagination
            v-model:current-page="pageParams.current"
            v-model:page-size="pageParams.size"
            background
            layout="total, sizes, prev, pager, next, jumper"
            :total="pageParams.total"
            @change="onLoad"
          />
        </div>
      </div>
    </div>
  </cm-container>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import CmContainer from '@/components/cm-container/main.vue';
import { useResizeObserver } from '@/hooks/useResizeObserver';
import useBusinessData from '@/hooks/useBusinessData';
const { njList,bjList } = useBusinessData('nj', 'bj');
import {classTodoPageApi,} from '@/api/todo';
// 搜索参数
const searchParams = ref({
   nj:'',
   bjbh:'',
});

// 分页参数
const pageParams = ref({
  total: 0,
  current: 1,
  size: 10,
});


// 表格列定义 - 班级
const columns = [
  { prop: 'yjbh', label: '预警批次', width: '100' },
  { prop: 'xymc', label: '预警院系',},
  { prop: 'zymc', label: '预警专业',  },
  { prop: 'nj', label: '预警年级',  },
  { prop: 'bjmc', label: '预警班级',},
  { prop: 'yjlx', label: '产生预警类型', },
  { prop: 'warningCount', label: '预警人数', width: '80' },
  { prop: 'courseCount', label: '预警课程数', width: '100' },
  { prop: 'clzt', label: '处理状态', width: '100' },
  { prop: 'clsj', label: '处理时间', width: '120' },
  { prop: 'operation', label: '操作', width: '160' }
];

// 表格数据 - 班级
const tableData = ref([]);

// 表格引用
const tableRef = useTemplateRef('tableRef');

// 表格高度
const tableHeight = ref(0);

// 初始化
onMounted(() => {
  useResizeObserver(tableRef, entries => {
    const entry = entries[0];
    const { height: _h } = entry.contentRect;
    tableHeight.value = _h - 54;
  });
  getClassTodoPage()
});

//班级列表
const  getClassTodoPage = () => {
  classTodoPageApi({ current:pageParams.value.current, size:pageParams.value.size,clzt:'1', ...searchParams.value })
    .then(result => {
      const {data, code } = result.data || {};
      if (code === 200) {
        tableData.value = data.records;
        pageParams.value.total = data.total;
      }
    })
};

// 搜索函数
const onSearch = () => {
  console.log(searchParams.value);
  getClassTodoPage()
};

// 清空函数
const onClear = () => {
  searchParams.value = {};
  getClassTodoPage()
};

const router = useRouter();

// 查看处理
const handle = (index, row) => {
  console.log(index, row);
  router.push({
    path: '/warningInfo/batchProcessing',
    query: {
      rowData: row,
      type:'view'
    },
  });
};
// 查看
const view = (index, row) => {
  console.log(index, row);
  router.push({
    path: '/todo/todoDetailedList',
    query: {
      rowData: row,
      type:'done'
    },
  });
};

// 加载数据
const onLoad = () => {
  console.log(pageParams.value);
};
</script>

<style lang="scss" scoped>
.warning-info {
  width: 100%;
  height: 100%;
  background-color: #fff;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  gap: 12px;

  .warning-tip {
    background-color: #ffecec;
    color: #f56c6c;
    padding: 8px;
  }

  .search-box {
    padding: 0 10px;
    margin-top: 0px;
  }


  .warning-info-table {
    padding: 0 10px;
    flex: 1;
  }
}
</style>