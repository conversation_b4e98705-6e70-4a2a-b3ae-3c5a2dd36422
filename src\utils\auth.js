import Cookies from 'js-cookie'
import website from '@/config/website'
import store from '@/store'

const TokenKey = website.tokenKey
const RefreshTokenKey = website.refreshTokenKey
const SessionId = 'JSESSIONID'
const UserId = 'b-user-id'

export function getToken() {
  return Cookies.get(TokenKey)
}

export function setToken(token) {
  return Cookies.set(TokenKey, token)
}

export function getRefreshToken() {
  return Cookies.get(RefreshTokenKey)
}

export function setRefreshToken(token) {
  return Cookies.set(RefreshTokenKey, token)
}

export function removeToken() {
  Cookies.remove(SessionId)
  Cookies.remove(UserId)
  return Cookies.remove(TokenKey)
}

export function removeRefreshToken() {
  return Cookies.remove(RefreshTokenKey)
}


/**
 * 检查用户是否拥有指定权限（性能优化版）
 * @param {string|Array} permission - 权限码或权限码数组
 * @returns {boolean} - 是否有权限
 */
export function hasAuth(permission) {
  // 快速参数验证 - 使用更高效的检查方式
  if (permission == null || permission === '') {
    // 仅在开发环境输出警告
    if (process.env.NODE_ENV === 'development') {
      console.warn('[v-perms] 权限参数不能为空')
    }
    return false
  }

  // 获取用户权限数据 - 移到参数验证之后，避免不必要的 store 访问
  const userPermissions = store.getters.permission || {}

  // 优化的类型检查和权限验证
  const permissionType = typeof permission

  if (permissionType === 'string') {
    // 单个权限码字符串 - 直接属性访问，最高效的方式
    return Reflect.has(userPermissions, permission) === true
  }

  if (Array.isArray(permission)) {
    // 权限码数组验证
    const length = permission.length
    if (length === 0) {
      if (process.env.NODE_ENV === 'development') {
        console.warn('[v-perms] 权限码数组不能为空')
      }
      return false
    }

    // 使用 for 循环替代 some()，在找到第一个匹配时立即返回，性能更好
    for (let i = 0; i < length; i++) {
      const code = permission[i]
      if (userPermissions[code] === true) {
        return true
      }
    }
    return false
  }

  // 不支持的参数类型
  if (process.env.NODE_ENV === 'development') {
    console.warn('[v-perms] 不支持的权限参数类型:', permissionType)
  }
  return false
}
