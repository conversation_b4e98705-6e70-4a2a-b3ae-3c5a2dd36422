import request from "@/axios";

/**
 * 获取消息列表
 * @param {Object} params
 * @returns {Promise}
 */
export const getMessageListAPI = (params) => {
  return request({
    url: "/blade-yjtsxxjl/yjTsXxJl/list",
    method: "get",
    params,
  });
};

/**
 * 全部标记已读
 * @param {Object} params
 * @returns {Promise}
 */
export const allReadAPI = (params) => {
  return request({
    url: "/blade-yjtsxxjl/yjTsXxJl/allRead",
    method: "get",
    params,
  });
};

/**
 * 查看消息
 * @param {Object} params
 * @param {string} params.id 消息ID
 * @returns {Promise}
 */
export const viewMessageAPI = (params) => {
  return request({
    url: "/blade-yjtsxxjl/yjTsXxJl/detail",
    method: "get",
    params,
  });
};
