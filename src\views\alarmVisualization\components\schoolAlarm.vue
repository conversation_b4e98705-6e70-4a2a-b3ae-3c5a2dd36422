<!-- 预警概况 -->
<!-- 预警概况 -->
<template>
    <div class="school-alarm">
        <div class="school-alarm-top">
            <div class="school-alarm-top-item1 column ">
                <div class="school-alarm-top-title number-left">总预警人数</div>
                <div class="school-alarm-top-number number-left">
                    <span class="school-alarm-top-number-item">{{ schoolAlarmData?.alarmPersonNum || 0 }}</span>
                    <span class="school-alarm-top-number-item2">人</span>
                </div>
            </div>
            <div class="school-alarm-top-item2 column">
                <div class="item2-number">
                    <span class="item2-number-item">{{ schoolAlarmData?.alarmRate || 0}}</span>
                    <span class="item2-number-item2">% </span>
                </div>
                <div class="item2-title">必修挂科率</div>
            </div>
            <div class="school-alarm-top-item3 column">
                <div class="school-alarm-top-title number-right">总预警次数</div>
                <div class="school-alarm-top-number number-right">
                    <span class="school-alarm-top-number-item">{{ schoolAlarmData?.alarmTotalNum || 0 }}</span>
                    <span class="school-alarm-top-number-item2">次</span>
                </div>
            </div>
        </div>
        <div class="school-alarm-bottom">
           <div class="bg-box">
             <div class="bg-box-item" v-for="(item, index) in alarmTypeData" :key="index">
                <img class="item-img" :src="item.icon" :alt="item.type">
                <div class="item-text">{{ item.text }}</div>
             </div>
           </div>
           <div class="text-box">
            <div class="text-box-item" v-for="(item, index) in alarmTypeLabels" :key="index">{{ item }}</div>
           </div>
        </div>
    </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  data: {
    type: Object,
    default: () => ({})
  }
})

const schoolAlarmData = computed(() => props.data || {})

// 底部预警类型数据
const alarmTypeData = computed(() => {
  if (!props.data) return []
  
  return [
    {
      icon: '/img/visualization/icon0.png',
      text: `${props.data.compulsoryAlarmCount || 0}次(${props.data.compulsoryAlarmPersonNum || 0}人)`
    },
    {
      icon: '/img/visualization/icon1.png',
      text: `${props.data.electiveAlarmCount || 0}次(${props.data.electiveAlarmPersonNum || 0}人)`
    },
    {
      icon: '/img/visualization/icon2.png',
      text: `${props.data.dgAlarmTotalNum || 0}次(${props.data.dgAlarmPersonNum || 0}人)`
    },
    {
      icon: '/img/visualization/icon3.png',
      text: `${props.data.byAlarmTotalNum || 0}次(${props.data.byAlarmPersonNum || 0}人)`
    }
  ]
})

const alarmTypeLabels = [
  '必修课程预警数',
  '选修课程预警数',
  '顶岗实习预警数',
  '毕业设计预警数'
]
</script>

<style lang="scss" scoped>
@use '@/styles/utils.scss' as utils;
.school-alarm {
    width: 100%;
    height: utils.vh(400px);
    position: relative;
    box-sizing: border-box;
    padding: utils.vw(12px);
    .school-alarm-top {
        width: 100%;
        height: utils.vh(224px);
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap:utils.vw(20px);
         margin-bottom: utils.vw(12px);
        .school-alarm-top-item1 {
            width: utils.vw(176px);
            height: utils.vh(80px);
            gap: utils.vw(5px);
            background-image: url('/img/largeScreen/major-alarm-right.png');
            background-size: 100% 100%;
            background-repeat: no-repeat;
        }
        .school-alarm-top-item2 {
            width: utils.vw(224px);
            height: utils.vh(224px);
            background-image: url('/img/largeScreen/major-centerBg2.png');
            background-size: 100% 100%;
            background-repeat: no-repeat;
            .item2-number{
                color: #fff;
                .item2-number-item{
                    font-size: utils.vw(28px);
                    font-family: 'DOUYUFont';
                }
                .item2-number-item2{
                    font-size: utils.vw(14px);
                }
            }
            .item2-title{
                font-family: 'DingTalkJinBuTi';
                font-size: utils.vw(14px);
                color: #C3E4FF;
            }
        }
        .school-alarm-top-item3 {
            width: utils.vw(176px);
            height: utils.vh(80px);
            gap: utils.vw(5px);
            background-image: url('/img/largeScreen/major-alarm-left.png');
            background-size: 100% 100%;
            background-repeat: no-repeat;
        }
        .school-alarm-top-title {
            font-size: utils.vw(14px);
            color: #fff;
            text-align: center;
        }
        .school-alarm-top-number{
            .school-alarm-top-number-item{
                font-size: utils.vw(20px);
                color: #00FEFF;
                font-family: 'DOUYUFont';
            }
            .school-alarm-top-number-item2{
                font-size: utils.vw(12px);
                color: #fff;
                margin-left:utils.vw(5px);
            }
        }
        .number-left{
            margin-right: utils.vw(20px);
        }
        .number-right{
            margin-left: utils.vw(20px);
        }
        .column {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }
    }
    .school-alarm-bottom {
        width: 100%;
        height: utils.vh(165px);
         
        .bg-box{
            display: flex;
            // justify-content: space-around;
            text-align: center;
           padding: 0px  utils.vw(10px);
           gap:  utils.vw(16px);
           .bg-box-item{
            width:100%;
             height: utils.vh(100px);
             background-image: url('/img/visualization/iconBg.png');
             background-size: 100% 100%;
             .item-img{
                width: 30%;
            height:40%;
             }
             .item-text{
                font-family: DINPro, DINPro;
                font-weight: 500;
                font-size: 15px;
                color: #7DDDF9;
                line-height: 20px;
                text-align: center;
                font-style: normal;
                // background: linear-gradient(90deg, #FFFFFF 0%, #71C2E5 100%);
             }
           }
        }
        .text-box{
            display: flex;
            justify-content: space-around;
            text-align: center;
            padding: 0px 10px;
           gap:  utils.vw(16px);
           div{
            font-family: DingTalk, DingTalk;
            font-weight: normal;
            font-size: 14px;
            color: #89CCED;
            line-height: 20px;
            text-align: center;
            font-style: normal;
           }
        }
    }
}
</style>

