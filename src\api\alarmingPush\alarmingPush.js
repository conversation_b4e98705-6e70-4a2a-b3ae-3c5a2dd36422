import request from "@/axios";

// 预警推送列表
export const tsXxList = (pageNum, pageSize, params) => {
  return request({
    url: "/blade-tsxx/TsXx/tsXxList",
    method: "get",
    params: {
      ...params,
      pageNum,
      pageSize,
    },
  });
};
//启停自动预警
export const updateByKey = (data) => {
  return request({
    url: "/blade-system/param/updateByKey",
    method: "post",
    data,
  });
};

// 获取自动预警启停状态
export const detail = (params) => {
  return request({
    url: "/blade-system/param/detail",
    method: "get",
    params,
  });
};

// 预警推送记录列表
export const listTsSj = (params) => {
  return request({
    url: "/blade-tssj/TsSj/listTsSj",
    method: "get",
    params,
  });
};

// 删除预警推送记录
export const removeSj = (data) => {
  return request({
    url: "/blade-tssj/TsSj/removeSj",
    method: "post",
    data,
  });
};

// 自动推送时间配置
export const addTsSj = (data) => {
  return request({
    url: "/blade-tssj/TsSj/addTsSj",
    method: "post",
    data,
  });
};

// 根据专业查询预警信息
export const pageByMajor = (params) => {
  return request({
    url: "/blade-yjxxcj/yjxxCj/pageByMajor",
    method: "get",
    params,
  });
};

// 根据班级查询预警信息
export const pageByClass = (params) => {
  return request({
    url: "/blade-yjxxcj/yjxxCj/pageByClass",
    method: "get",
    params,
  });
};
// 学生核验列表
export const checkStudentList = (params) => {
  return request({
    url: "/blade-yjxxcj/yjxxCj/checkStudentList",
    method: "get",
    params,
  });
};

// 保存核验结果
export const saveCheckData = (data) => {
  return request({
    url: "/blade-yjxxcj/yjxxCj/saveCheckData",
    method: "post",
    data,
  });
};

// 推送预警
export const push = (data) => {
  return request({
    url: "/blade-yjxxcj/yjxxCj/push",
    method: "post",
    data,
  });
};

// 新增预警推送
export const addTsXx = (data) => {
  return request({
    url: "/blade-tsxx/TsXx/addTsXx",
    method: "post",
    data,
  });
};
